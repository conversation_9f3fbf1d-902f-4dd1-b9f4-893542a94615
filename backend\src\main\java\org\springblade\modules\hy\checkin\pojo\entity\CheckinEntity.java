/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hy.checkin.pojo.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springblade.core.mp.base.BaseEntity;

/**
 * 参会签到记录表实体类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@TableName("hy_checkin")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "参会签到记录表")
public class CheckinEntity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @TableField("user_id")
    private Long userId;

    /**
     * 签到时间
     */
    @Schema(description = "签到时间")
    @TableField("checkin_time")
    private LocalDateTime checkinTime;

    /**
     * 签到类型
     */
    @Schema(description = "签到类型")
    @TableField("checkin_type")
    private String checkinType;

    /**
     * 签到位置
     */
    @Schema(description = "签到位置")
    @TableField("location")
    private String location;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    @TableField("device_info")
    private String deviceInfo;

    /**
     * 二维码内容
     */
    @Schema(description = "二维码内容")
    @TableField("qr_code_text")
    private String qrCodeText;

    /**
     * 签到状态
     */
    @Schema(description = "签到状态")
    @TableField("checkin_status")
    private String checkinStatus;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @TableField("remark")
    private String remark;

}
