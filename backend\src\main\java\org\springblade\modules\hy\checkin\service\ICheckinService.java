/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hy.checkin.service;

import org.springblade.modules.hy.checkin.pojo.entity.CheckinEntity;
import org.springblade.modules.hy.checkin.pojo.vo.CheckinVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 参会签到记录表 服务类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface ICheckinService extends BaseService<CheckinEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param checkin
	 * @return
	 */
	IPage<CheckinVO> selectCheckinPage(IPage<CheckinVO> page, CheckinVO checkin);

	/**
	 * 验证二维码签到
	 *
	 * @param qrCodeText 二维码内容
	 * @return 验证结果
	 */
	boolean validateQRCodeCheckin(String qrCodeText);

	/**
	 * 提交签到记录
	 *
	 * @param checkinEntity 签到实体
	 * @return 签到结果
	 */
	boolean submitCheckin(CheckinEntity checkinEntity);

	/**
	 * 获取用户今日签到状态
	 *
	 * @param userId 用户ID
	 * @return 签到记录
	 */
	CheckinVO getTodayCheckinStatus(Long userId);

	/**
	 * 检查用户今日是否已签到
	 *
	 * @param userId 用户ID
	 * @return 是否已签到
	 */
	boolean isCheckedInToday(Long userId);

	/**
	 * 统计今日签到人数
	 *
	 * @return 签到人数
	 */
	Integer countTodayCheckin();

}
