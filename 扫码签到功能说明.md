# 扫码签到功能实现说明

## 功能概述

本次实现了一个完整的扫码签到功能，支持钉钉JSAPI扫码和模拟扫码测试。用户可以通过扫描二维码进行会议签到，系统会记录签到时间、位置、设备信息等详细信息。

## ✨ 重要更新：全局钉钉JSAPI集成

**已将钉钉JSAPI从页面级别提升到全局级别，提供更好的架构和用户体验：**

1. **全局JSAPI加载** - 在 `index.html` 中全局引入钉钉JSAPI
2. **统一工具类** - 创建 `@/utils/dingtalk.js` 统一管理钉钉功能
3. **配置文件** - 创建 `@/config/dingtalk.js` 集中管理配置
4. **环境检测组件** - 提供可视化的钉钉环境检测界面
5. **全局注册** - 在 `main.js` 中全局注册，所有组件可通过 `this.$dingTalk` 访问

## 实现的功能

### 前端功能
1. **扫码签到入口** - 在主页面添加了扫码签到功能卡片
2. **钉钉JSAPI集成** - 支持在钉钉客户端中调用摄像头扫码
3. **模拟扫码测试** - 在非钉钉环境中提供模拟扫码功能用于开发测试
4. **签到状态反馈** - 提供签到成功/失败的用户反馈
5. **二维码验证** - 验证扫描的二维码是否有效

### 后端功能
1. **签到记录管理** - 完整的签到记录CRUD操作
2. **二维码验证** - 验证二维码内容的有效性
3. **重复签到检查** - 防止用户重复签到
4. **签到统计** - 统计今日签到人数
5. **签到状态查询** - 查询用户今日签到状态

## 文件结构

### 前端文件
```
frontend/
├── index.html                         # 全局引入钉钉JSAPI
├── src/
│   ├── main.js                        # 全局注册钉钉工具类
│   ├── views/wel/index.vue            # 主页面，添加了扫码签到入口
│   ├── api/checkin/checkin.js         # 签到相关API接口
│   ├── utils/dingtalk.js              # 钉钉工具类（核心）
│   ├── config/dingtalk.js             # 钉钉配置文件
│   └── components/DingTalkDetector.vue # 钉钉环境检测组件
```

### 后端文件
```
backend/src/main/java/org/springblade/modules/hy/checkin/
├── controller/CheckinController.java   # 签到控制器
├── service/ICheckinService.java       # 签到服务接口
├── service/impl/CheckinServiceImpl.java # 签到服务实现
├── mapper/CheckinMapper.java          # 签到数据访问层
├── pojo/entity/CheckinEntity.java     # 签到实体类
└── pojo/vo/CheckinVO.java             # 签到视图对象

backend/src/main/resources/org/springblade/modules/hy/checkin/
└── CheckinMapper.xml                  # MyBatis映射文件

backend/doc/sql/
└── hy_checkin_table.sql              # 签到表创建SQL
```

## 使用方法

### 1. 数据库初始化
执行SQL文件创建签到表：
```sql
-- 执行 backend/doc/sql/hy_checkin_table.sql
```

### 2. 在钉钉客户端中使用
1. 在钉钉客户端中打开会议系统
2. 点击主页面的"扫码签到"功能卡片
3. 系统会调用钉钉JSAPI唤起摄像头
4. 扫描会议签到二维码
5. 系统验证二维码并记录签到信息

### 3. 开发测试模式
1. 在浏览器中打开会议系统
2. 点击"扫码签到"功能卡片
3. 系统会提示使用模拟扫码功能
4. 确认后会生成模拟二维码进行测试

## API接口说明

### 签到相关接口

#### 1. 提交签到记录
- **URL**: `POST /hy/checkin/submit`
- **描述**: 用户扫码或手动签到
- **参数**: 
```json
{
  "qrCodeText": "二维码内容",
  "checkinTime": "2025-08-04T08:30:00",
  "checkinType": "qrcode",
  "location": "会议现场",
  "deviceInfo": "设备信息"
}
```

#### 2. 验证二维码
- **URL**: `POST /hy/checkin/validate-qrcode`
- **描述**: 验证扫描的二维码是否有效
- **参数**:
```json
{
  "qrCodeText": "二维码内容"
}
```

#### 3. 获取今日签到状态
- **URL**: `GET /hy/checkin/today-status`
- **描述**: 查询当前用户今日签到状态

#### 4. 统计今日签到人数
- **URL**: `GET /hy/checkin/today-count`
- **描述**: 统计今日总签到人数

#### 5. 签到记录列表
- **URL**: `GET /hy/checkin/list`
- **描述**: 分页查询签到记录列表

## 技术特点

### 1. 钉钉JSAPI集成
- 动态加载钉钉JSAPI库
- 支持钉钉环境检测
- 调用钉钉扫码功能

### 2. 优雅降级
- 在非钉钉环境中提供模拟扫码功能
- 确保开发和测试的便利性

### 3. 数据安全
- 二维码内容验证
- 重复签到检查
- 用户身份验证

### 4. 用户体验
- 清晰的操作反馈
- 友好的错误提示
- 响应式设计

## 扩展功能建议

1. **签到二维码生成** - 为不同会议/时段生成专用签到二维码
2. **地理位置验证** - 结合GPS定位验证签到位置
3. **签到统计报表** - 提供详细的签到数据分析
4. **批量签到管理** - 支持管理员批量处理签到记录
5. **签到提醒** - 会议开始前提醒用户签到

## 注意事项

1. 确保在钉钉客户端中使用以获得最佳体验
2. 二维码内容需要包含特定标识符才能通过验证
3. 每个用户每天只能签到一次
4. 签到记录会永久保存，支持历史查询

## 联系方式

如有问题或建议，请联系开发团队。
