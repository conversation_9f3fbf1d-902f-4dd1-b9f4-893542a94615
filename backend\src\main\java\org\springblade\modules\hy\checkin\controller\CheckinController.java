/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hy.checkin.controller;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.secure.BladeUser;
import org.springblade.core.secure.utils.AuthUtil;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.modules.hy.checkin.pojo.entity.CheckinEntity;
import org.springblade.modules.hy.checkin.pojo.vo.CheckinVO;
import org.springblade.modules.hy.checkin.service.ICheckinService;
import org.springblade.core.boot.ctrl.BladeController;
import java.util.Map;

/**
 * 参会签到记录表 控制器
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@RestController
@AllArgsConstructor
@RequestMapping("hy/checkin")
@Tag(name = "会议签到管理", description = "会议签到功能，包括扫码签到、签到记录查询、签到状态查询等")
public class CheckinController extends BladeController {

	private final ICheckinService checkinService;

	/**
	 * 签到记录详情查询
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "获取签到记录详情", description = "根据签到记录ID查询详细信息")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<CheckinVO> detail(@Parameter(description = "签到记录实体对象，包含查询条件") CheckinEntity checkin) {
		CheckinEntity detail = checkinService.getOne(Condition.getQueryWrapper(checkin));
		CheckinVO checkinVO = new CheckinVO();
		if (detail != null) {
			// 这里可以添加数据转换逻辑
			org.springframework.beans.BeanUtils.copyProperties(detail, checkinVO);
		}
		return R.data(checkinVO);
	}

	/**
	 * 签到记录分页查询
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页查询签到记录", description = "分页查询签到记录列表，支持多种查询条件")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "400", description = "请求参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<IPage<CheckinVO>> list(@Parameter(hidden = true) @RequestParam Map<String, Object> checkin, Query query) {
		IPage<CheckinEntity> pages = checkinService.page(Condition.getPage(query), Condition.getQueryWrapper(checkin, CheckinEntity.class));
		// 转换为VO
		IPage<CheckinVO> voPages = pages.convert(entity -> {
			CheckinVO vo = new CheckinVO();
			org.springframework.beans.BeanUtils.copyProperties(entity, vo);
			return vo;
		});
		return R.data(voPages);
	}

	/**
	 * 提交签到记录
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "提交签到记录", description = "用户扫码或手动签到")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "签到成功"),
		@ApiResponse(responseCode = "400", description = "签到失败，可能是重复签到或参数错误"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R submitCheckin(@Parameter(description = "签到记录信息") @Valid @RequestBody CheckinEntity checkin) {
		// 检查今日是否已签到
		if (checkinService.isCheckedInToday(AuthUtil.getUserId())) {
			return R.fail("今日已签到，请勿重复签到");
		}
		
		boolean result = checkinService.submitCheckin(checkin);
		if (result) {
			return R.success("签到成功");
		} else {
			return R.fail("签到失败");
		}
	}

	/**
	 * 验证二维码签到
	 */
	@PostMapping("/validate-qrcode")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "验证二维码签到", description = "验证扫描的二维码是否有效")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "验证成功"),
		@ApiResponse(responseCode = "400", description = "二维码无效"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R validateQRCodeCheckin(@Parameter(description = "二维码内容") @RequestBody Map<String, String> params) {
		String qrCodeText = params.get("qrCodeText");
		boolean isValid = checkinService.validateQRCodeCheckin(qrCodeText);
		if (isValid) {
			return R.success("二维码验证成功");
		} else {
			return R.fail("无效的签到二维码");
		}
	}

	/**
	 * 获取今日签到状态
	 */
	@GetMapping("/today-status")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "获取今日签到状态", description = "查询当前用户今日签到状态")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "查询成功"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<CheckinVO> getTodayCheckinStatus() {
		CheckinVO checkinStatus = checkinService.getTodayCheckinStatus(AuthUtil.getUserId());
		return R.data(checkinStatus);
	}

	/**
	 * 统计今日签到人数
	 */
	@GetMapping("/today-count")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "统计今日签到人数", description = "统计今日总签到人数")
	@ApiResponses({
		@ApiResponse(responseCode = "200", description = "统计成功"),
		@ApiResponse(responseCode = "500", description = "服务器内部错误")
	})
	public R<Integer> getTodayCheckinCount() {
		Integer count = checkinService.countTodayCheckin();
		return R.data(count);
	}

}
