<template>
  <div class="container">
    <!-- 主标题区域 -->
    <section class="title-section" v-show="!currentComponent">
      <h1 class="main-title">{{ mainTitle || '数智攀登·管理跃升' }}</h1>
      <h2 class="conference-title">{{ subTitle || '企业管理现场会' }}</h2>
      <p class="organizer">{{ organizer || '主办单位：广东烟草广州市有限公司'}}</p>
      <p class="date">{{ date || '2025年9月'}}</p>
    </section>

    <!-- 页面头部 -->
    <header class="page-header" v-show="currentComponent">
      <button class="back-btn" @click="goBack">
        <i class="fas fa-arrow-left"></i>
      </button>
      <h1>{{ getComponentTitle() }}</h1>
      <div class="header-placeholder"></div>
    </header>

    <!-- 功能模块区域 -->
    <section class="function-grid" v-show="!currentComponent">
      <div class="row-1">
        <div class="function-card" @click="showComponent('agenda')">
          <div class="card-icon">
            <i class="fas fa-calendar-alt"></i>
          </div>
          <div class="card-text">
            <h3>会议议程</h3>
            <p>AGENDA</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('live-stream')">
          <div class="card-icon">
            <i class="fas fa-broadcast-tower"></i>
          </div>
          <div class="card-text">
            <h3>云直播</h3>
            <p>LIVE STREAM</p>
          </div>
        </div>
      </div>
      <div class="row-2">
        <div class="function-card" @click="showComponent('sub-venues')">
          <div class="card-icon">
            <i class="fas fa-video"></i>
          </div>
          <div class="card-text">
            <h3>分会场信息</h3>
            <p>SUB VENUES</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('materials')">
          <div class="card-icon">
            <i class="fas fa-file-alt"></i>
          </div>
          <div class="card-text">
            <h3>会议资料</h3>
            <p>MATERIALS</p>
          </div>
        </div>
      </div>
      <div class="row-3">
        <div class="function-card" @click="showComponent('photo')">
          <div class="card-icon">
            <i class="fas fa-images"></i>
          </div>
          <div class="card-text">
            <h3>在线相册</h3>
            <p>PHOTO</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('guide')">
          <div class="card-icon">
            <i class="fas fa-book-open"></i>
          </div>
          <div class="card-text">
            <h3>参会指南</h3>
            <p>GUIDE</p>
          </div>
        </div>
      </div>
      <div class="row-4">
        <div class="function-card" @click="showComponent('ai-chat')">
          <div class="card-icon">
            <i class="fas fa-robot"></i>
          </div>
          <div class="card-text">
            <h3>会务助手</h3>
            <p>AI ASSISTANT</p>
          </div>
        </div>
        <div class="function-card" @click="showComponent('profile')">
          <div class="card-icon">
            <i class="fas fa-user"></i>
          </div>
          <div class="card-text">
            <h3>个人中心</h3>
            <p>PROFILE</p>
          </div>
        </div>
      </div>
      <div class="row-5">
        <div class="function-card scan-checkin-card" @click="handleScanCheckin">
          <div class="card-icon">
            <i class="fas fa-qrcode"></i>
          </div>
          <div class="card-text">
            <h3>扫码签到</h3>
            <p>SCAN CHECKIN</p>
          </div>
        </div>
      </div>
    </section>

    <!-- 动态组件显示区域 -->
    <div class="component-container" v-show="currentComponent">
      <component :is="currentComponentName" @navigate="handleNavigate" />
    </div>
  </div>
</template>

<script>
// 导入所有需要的组件
import Agenda from './Agenda.vue'
import LiveStream from './LiveStream.vue'
import SubVenues from './SubVenues.vue'
import Materials from './Materials.vue'
import Photo from './Photo.vue'
import Guide from './Guide.vue'
import AiChat from './AiChat.vue'
import Profile from './Profile.vue'
import MyDining from './MyDining.vue'
import MyAccommodation from './MyAccommodation.vue'
import MySchedule from './MySchedule.vue'
import { getDictionary } from '@/api/system/dictbiz'
import { submitCheckin, validateQRCodeCheckin } from '@/api/checkin/checkin'

export default {
  name: 'HomeH5Like',
  components: {
    Agenda,
    LiveStream,
    SubVenues,
    Materials,
    Photo,
    Guide,
    AiChat,
    Profile,
    MyDining,
    MyAccommodation,
    MySchedule
  },
  data() {
    return {
      currentComponent: null, // 当前显示的组件名称
      previousComponent: null, // 上一个组件，用于返回导航
      mainTitle: '', // 主标题，从字典获取
      subTitle:'',
      date:'',
      organizer:''
    }
  },
  computed: {
    currentComponentName() {
      if (!this.currentComponent) return null

      // 将组件名称映射到实际的组件名
      const componentMap = {
        'agenda': 'Agenda',
        'live-stream': 'LiveStream',
        'sub-venues': 'SubVenues',
        'materials': 'Materials',
        'photo': 'Photo',
        'guide': 'Guide',
        'ai-chat': 'AiChat',
        'profile': 'Profile',
        'my-dining': 'MyDining',
        'my-accommodation': 'MyAccommodation',
        'my-schedule': 'MySchedule'
      }

      return componentMap[this.currentComponent]
    }
  },
  async mounted() {
    // 动态引入Font Awesome
    if (!document.getElementById('fa-h5-home')) {
      const link = document.createElement('link');
      link.id = 'fa-h5-home';
      link.rel = 'stylesheet';
      link.href = 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css';
      document.head.appendChild(link);
    }
    // 动态引入demo的js动画
    if (!document.getElementById('demo-main-js')) {
      const script = document.createElement('script');
      script.id = 'demo-main-js';
      script.src = '/pages/js/main.js';
      document.body.appendChild(script);
    }

    // 动态引入钉钉JSAPI
    await this.loadDingTalkJSAPI();

    // 获取主标题
    await this.loadData();
  },
  methods: {
    /**
     * 加载钉钉JSAPI
     */
    async loadDingTalkJSAPI() {
      return new Promise((resolve, reject) => {
        // 检查是否已经加载过钉钉JSAPI
        if (window.dd) {
          resolve();
          return;
        }

        // 动态加载钉钉JSAPI
        const script = document.createElement('script');
        script.src = 'https://g.alicdn.com/dingding/open-develop/1.9.0/dingtalk.js';
        script.onload = () => {
          console.log('钉钉JSAPI加载成功');
          resolve();
        };
        script.onerror = () => {
          console.error('钉钉JSAPI加载失败');
          // 即使加载失败也resolve，这样不会阻塞页面加载
          resolve();
        };
        document.head.appendChild(script);
      });
    },

    /**
     * 处理扫码签到
     */
    async handleScanCheckin() {
      try {
        // 检查是否在钉钉环境中
        if (!window.dd) {
          // 如果不在钉钉环境中，提供模拟扫码功能用于测试
          this.simulateScanCheckin();
          return;
        }

        // 钉钉环境检查
        window.dd.ready(() => {
          // 调用钉钉扫码API
          window.dd.biz.util.scan({
            type: "qr", // 扫码类型，qr表示二维码
            onSuccess: (result) => {
              console.log('扫码成功:', result);
              this.processCheckinResult(result.text);
            },
            onFail: (err) => {
              console.error('扫码失败:', err);
              this.$message?.error('扫码失败，请重试') || alert('扫码失败，请重试');
            }
          });
        });
      } catch (error) {
        console.error('扫码签到出错:', error);
        this.$message?.error('扫码功能暂时不可用') || alert('扫码功能暂时不可用');
      }
    },

    /**
     * 模拟扫码签到（用于测试）
     */
    simulateScanCheckin() {
      // 模拟二维码内容
      const mockQRCode = 'conference_checkin_' + new Date().getTime();

      // 显示确认对话框
      const confirmed = confirm(`检测到您不在钉钉环境中，是否使用模拟扫码进行测试？\n\n模拟二维码内容：${mockQRCode}`);

      if (confirmed) {
        this.processCheckinResult(mockQRCode);
      }
    },

    /**
     * 处理签到结果
     */
    async processCheckinResult(qrCodeText) {
      try {
        // 显示处理中状态
        this.$message?.info('正在处理签到...') || console.log('正在处理签到...');

        // 首先验证二维码是否有效
        const validateResponse = await validateQRCodeCheckin(qrCodeText);

        if (!validateResponse.data.success) {
          this.$message?.error(validateResponse.data.message || '无效的签到二维码') || alert(validateResponse.data.message || '无效的签到二维码');
          return;
        }

        // 构建签到数据
        const checkinData = {
          qrCodeText: qrCodeText,
          checkinTime: new Date(),
          checkinType: 'qrcode',
          location: '会议现场',
          deviceInfo: navigator.userAgent
        };

        // 调用签到API
        const response = await submitCheckin(checkinData);

        if (response.data.success) {
          this.$message?.success('签到成功！') || alert('签到成功！');
        } else {
          this.$message?.error(response.data.message || '签到失败') || alert(response.data.message || '签到失败');
        }
      } catch (error) {
        console.error('处理签到结果出错:', error);
        if (error.response && error.response.data && error.response.data.msg) {
          this.$message?.error(error.response.data.msg) || alert(error.response.data.msg);
        } else {
          this.$message?.error('签到处理失败，请重试') || alert('签到处理失败，请重试');
        }
      }
    },

    /**
     * 加载主标题
     */
    async loadData() {
      const response = await getDictionary({
          code: 'hy_prop' // 字典编码，需要在后台配置
        });
        console.log('API响应:', response);
        // 检查响应格式
        if (response && response.data && response.data.success) {
          const dictData = response.data.data;
          if (dictData && Array.isArray(dictData) && dictData.length > 0) {
            // 从字典数据中提取文本
          this.mainTitle = dictData.find(item => item.dictValue === '会议主标题').dictKey;
          this.subTitle= dictData.find(item => item.dictValue === '会议副标题').dictKey;
          this.date= dictData.find(item => item.dictValue === '会议时间').dictKey;
          this.organizer= dictData.find(item => item.dictValue === '主办单位').dictKey;
          } else {
            console.log('API返回数据为空');
          }
        } else {
          throw new Error('API响应格式不正确');
        }
    },

    showComponent(componentName) {
      this.currentComponent = componentName
    },
    goBack() {
      // 如果有上一个组件，返回到上一个组件，否则返回首页
      if (this.previousComponent) {
        this.currentComponent = this.previousComponent
        this.previousComponent = null
      } else {
        this.currentComponent = null
      }
    },
    handleNavigate(componentName) {
      this.previousComponent = this.currentComponent
      this.currentComponent = componentName
    },
    getComponentTitle() {
      const titleMap = {
        'agenda': '会议议程',
        'live-stream': '云直播',
        'sub-venues': '分会场信息',
        'materials': '会议资料',
        'photo': '在线相册',
        'guide': '参会指南',
        'ai-chat': '会务助手',
        'profile': '个人中心',
        'my-dining': '我的用餐',
        'my-accommodation': '我的住宿',
        'my-schedule': '我的日程'
      }
      return titleMap[this.currentComponent] || ''
    }
  }
}
</script>

<style scoped>
/* demo/css/style.css 完全复制粘贴到这里 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 确保背景渐变应用到正确的元素 */
body, 
.h5-home-bg,
.container {
    font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
    min-height: 100vh;
    overflow-x: hidden;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%) !important;
}

.container {
    max-width: 375px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    position: relative;
    background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%) !important;
}

.title-section {
    text-align: center;
    margin-bottom: 40px;
    color: white;
    padding-top: 30px;
}

.main-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 8px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.conference-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    letter-spacing: 1px;
}

.organizer, .date {
    font-size: 14px;
    margin-bottom: 5px;
    opacity: 0.9;
}

.function-grid {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.row-1, .row-2, .row-3, .row-4, .row-5 {
    display: flex;
    gap: 15px;
}

.row-5 {
    justify-content: center;
}

.scan-checkin-card {
    max-width: 200px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    border: 2px solid rgba(255, 255, 255, 0.4);
    position: relative;
    overflow: hidden;
}

.scan-checkin-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.2), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.scan-checkin-card:hover::before {
    animation: scanShimmer 0.8s ease-in-out;
}

@keyframes scanShimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

.scan-checkin-card:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.3));
    border: 2px solid rgba(255, 215, 0, 0.6);
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 215, 0, 0.3);
}

.scan-checkin-card:hover .card-icon i {
    color: #FFD700;
    text-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    animation: pulse 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
    100% {
        transform: scale(1);
    }
}

.scan-checkin-card:hover .card-text h3 {
    color: #FFD700;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.6);
}

.function-card {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    flex: 1;
}

.function-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.6s ease;
    opacity: 0;
}

.function-card:hover::before {
    animation: shimmer 0.6s ease-in-out;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
        opacity: 0;
    }
}

.function-card:hover {
    transform: translateY(-8px) scale(1.02);
    background: rgba(255, 255, 255, 0.4);
    box-shadow: 0 15px 35px rgba(70, 130, 180, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.5);
}

.card-icon {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
}

.function-card:hover .card-icon {
    transform: scale(1.2) rotateY(360deg);
}

.card-icon i {
    font-size: 24px;
    color: white;
    margin-bottom: 8px;
    transition: all 0.3s ease;
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.function-card:hover .card-icon i {
    color: #87CEEB;
    text-shadow: 0 0 20px rgba(135, 206, 235, 0.8);
}

.card-text {
    text-align: center;
    color: white;
    transition: all 0.3s ease;
    position: relative;
}

.function-card:hover .card-text {
    transform: translateY(-2px);
}

.card-text h3 {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 2px;
    transition: all 0.3s ease;
}

.function-card:hover .card-text h3 {
    color: #87CEEB;
    text-shadow: 0 0 10px rgba(135, 206, 235, 0.6);
}

.card-text p {
    font-size: 10px;
    opacity: 0.8;
    letter-spacing: 0.5px;
    margin: 0;
    transition: all 0.3s ease;
}

.function-card:hover .card-text p {
    opacity: 1;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.9);
}

/* 页面头部 */
.page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 20px 0 20px;
    background: transparent;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.page-header h1 {
    color: white;
    font-size: 18px;
    font-weight: 500;
    margin: 0;
}

.header-placeholder {
    width: 40px;
}

/* 组件容器样式 */
.component-container {
    background: transparent;
    padding: 0;
    margin-top: 0;
    min-height: 400px;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style> 
