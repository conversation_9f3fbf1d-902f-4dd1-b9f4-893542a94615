/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hy.checkin.service.impl;

import org.springblade.modules.hy.checkin.pojo.entity.CheckinEntity;
import org.springblade.modules.hy.checkin.pojo.vo.CheckinVO;
import org.springblade.modules.hy.checkin.mapper.CheckinMapper;
import org.springblade.modules.hy.checkin.service.ICheckinService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springblade.core.tool.utils.Func;
import org.springblade.core.secure.utils.AuthUtil;
import java.time.LocalDateTime;
import java.time.LocalDate;
import lombok.extern.slf4j.Slf4j;

/**
 * 参会签到记录表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Slf4j
@Service
public class CheckinServiceImpl extends BaseServiceImpl<CheckinMapper, CheckinEntity> implements ICheckinService {

	@Override
	public IPage<CheckinVO> selectCheckinPage(IPage<CheckinVO> page, CheckinVO checkin) {
		return page.setRecords(baseMapper.selectCheckinPage(page, checkin));
	}

	@Override
	public boolean validateQRCodeCheckin(String qrCodeText) {
		// 这里可以实现二维码验证逻辑
		// 例如：验证二维码格式、有效期、会议信息等
		if (Func.isEmpty(qrCodeText)) {
			return false;
		}
		
		// 简单的验证逻辑：检查二维码是否包含会议标识
		// 实际项目中可以根据具体需求实现更复杂的验证逻辑
		return qrCodeText.contains("conference") || qrCodeText.contains("meeting") || qrCodeText.contains("checkin");
	}

	@Override
	public boolean submitCheckin(CheckinEntity checkinEntity) {
		try {
			// 设置当前用户ID
			if (checkinEntity.getUserId() == null) {
				checkinEntity.setUserId(AuthUtil.getUserId());
			}
			
			// 设置签到时间
			if (checkinEntity.getCheckinTime() == null) {
				checkinEntity.setCheckinTime(LocalDateTime.now());
			}
			
			// 检查今日是否已签到
			if (isCheckedInToday(checkinEntity.getUserId())) {
				log.warn("用户{}今日已签到", checkinEntity.getUserId());
				return false;
			}
			
			// 设置签到状态
			checkinEntity.setCheckinStatus("success");
			
			// 保存签到记录
			return this.save(checkinEntity);
		} catch (Exception e) {
			log.error("签到失败", e);
			return false;
		}
	}

	@Override
	public CheckinVO getTodayCheckinStatus(Long userId) {
		if (userId == null) {
			userId = AuthUtil.getUserId();
		}
		return baseMapper.selectTodayCheckinByUserId(userId);
	}

	@Override
	public boolean isCheckedInToday(Long userId) {
		if (userId == null) {
			userId = AuthUtil.getUserId();
		}
		
		LambdaQueryWrapper<CheckinEntity> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(CheckinEntity::getUserId, userId)
			   .ge(CheckinEntity::getCheckinTime, LocalDate.now().atStartOfDay())
			   .lt(CheckinEntity::getCheckinTime, LocalDate.now().plusDays(1).atStartOfDay())
			   .eq(CheckinEntity::getCheckinStatus, "success");
		
		return this.count(wrapper) > 0;
	}

	@Override
	public Integer countTodayCheckin() {
		return baseMapper.countTodayCheckin();
	}

}
