/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hy.checkin.pojo.vo;

import org.springblade.modules.hy.checkin.pojo.entity.CheckinEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 参会签到记录表视图实体类
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "参会签到记录表")
public class CheckinVO extends CheckinEntity {
	private static final long serialVersionUID = 1L;

	/**
	 * 用户姓名
	 */
	@Schema(description = "用户姓名")
	private String userName;

	/**
	 * 用户账号
	 */
	@Schema(description = "用户账号")
	private String userAccount;

	/**
	 * 用户手机号
	 */
	@Schema(description = "用户手机号")
	private String userPhone;

	/**
	 * 用户邮箱
	 */
	@Schema(description = "用户邮箱")
	private String userEmail;

	/**
	 * 签到状态名称
	 */
	@Schema(description = "签到状态名称")
	private String checkinStatusName;

	/**
	 * 签到类型名称
	 */
	@Schema(description = "签到类型名称")
	private String checkinTypeName;

}
