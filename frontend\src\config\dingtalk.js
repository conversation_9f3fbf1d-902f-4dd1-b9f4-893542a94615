/**
 * 钉钉配置文件
 * 包含钉钉应用的配置信息和常量
 */

export const DINGTALK_CONFIG = {
  // 钉钉应用配置
  APP_ID: process.env.VUE_APP_DINGTALK_APP_ID || '',
  APP_SECRET: process.env.VUE_APP_DINGTALK_APP_SECRET || '',
  
  // 钉钉JSAPI版本
  JSAPI_VERSION: '1.9.0',
  
  // 钉钉JSAPI CDN地址
  JSAPI_URL: 'https://g.alicdn.com/dingding/open-develop/1.9.0/dingtalk.js',
  
  // 扫码配置
  SCAN_CONFIG: {
    // 扫码类型
    TYPE: 'qr', // qr: 二维码, bar: 条形码, all: 全部
    
    // 二维码验证规则
    QR_CODE_PATTERNS: [
      /^conference_checkin_/,  // 会议签到二维码
      /^meeting_/,             // 会议相关二维码
      /^checkin_/              // 签到相关二维码
    ]
  },
  
  // 导航栏配置
  NAVIGATION: {
    // 默认标题
    DEFAULT_TITLE: '知慧会务系统',
    
    // 是否显示返回按钮
    SHOW_BACK: true,
    
    // 右侧按钮配置
    RIGHT_BUTTON: {
      text: '更多',
      show: false
    }
  },
  
  // 提示配置
  TOAST: {
    // 默认显示时长（秒）
    DURATION: 2,
    
    // 图标映射
    ICON_MAP: {
      success: 'success',
      error: 'error',
      info: 'loading',
      warning: 'loading'
    }
  },
  
  // 错误信息
  ERROR_MESSAGES: {
    NOT_IN_DINGTALK: '请在钉钉客户端中使用此功能',
    JSAPI_NOT_LOADED: '钉钉JSAPI未加载',
    SCAN_FAILED: '扫码失败，请重试',
    INVALID_QR_CODE: '无效的二维码',
    NETWORK_ERROR: '网络错误，请检查网络连接'
  }
};

/**
 * 钉钉环境检测
 */
export const DINGTALK_DETECTION = {
  /**
   * 检测是否在钉钉环境中
   * @returns {boolean}
   */
  isDingTalkApp() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('dingtalk');
  },
  
  /**
   * 检测是否在钉钉PC端
   * @returns {boolean}
   */
  isDingTalkPC() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('dingtalk') && !ua.includes('mobile');
  },
  
  /**
   * 检测是否在钉钉移动端
   * @returns {boolean}
   */
  isDingTalkMobile() {
    const ua = navigator.userAgent.toLowerCase();
    return ua.includes('dingtalk') && ua.includes('mobile');
  },
  
  /**
   * 获取钉钉版本信息
   * @returns {string|null}
   */
  getDingTalkVersion() {
    const ua = navigator.userAgent;
    const match = ua.match(/DingTalk\/(\d+\.\d+\.\d+)/);
    return match ? match[1] : null;
  }
};

/**
 * 钉钉权限配置
 */
export const DINGTALK_PERMISSIONS = {
  // 需要的权限列表
  REQUIRED_PERMISSIONS: [
    'device.camera',      // 摄像头权限（扫码需要）
    'device.notification', // 通知权限
    'biz.util.scan',      // 扫码权限
    'biz.user.get'        // 获取用户信息权限
  ],
  
  // 权限说明
  PERMISSION_DESCRIPTIONS: {
    'device.camera': '摄像头权限，用于扫码签到',
    'device.notification': '通知权限，用于显示操作结果',
    'biz.util.scan': '扫码权限，用于扫描二维码',
    'biz.user.get': '用户信息权限，用于获取当前用户信息'
  }
};

/**
 * 钉钉API端点配置
 */
export const DINGTALK_API_ENDPOINTS = {
  // 获取access_token
  GET_ACCESS_TOKEN: '/gettoken',
  
  // 获取用户信息
  GET_USER_INFO: '/user/getuserinfo',
  
  // 获取用户详情
  GET_USER_DETAIL: '/user/get',
  
  // 发送工作通知
  SEND_WORK_NOTICE: '/topapi/message/corpconversation/asyncsend_v2'
};

export default DINGTALK_CONFIG;
