/**
 * 钉钉JSAPI工具类
 * 提供钉钉环境检测、扫码、获取用户信息等功能
 */

import { DINGTALK_CONFIG, DINGTALK_DETECTION } from '@/config/dingtalk';

class DingTalkUtils {
  constructor() {
    this.isReady = false;
    this.readyCallbacks = [];
    this.init();
  }

  /**
   * 初始化钉钉JSAPI
   */
  init() {
    if (typeof window === 'undefined') {
      console.warn('DingTalk JSAPI 只能在浏览器环境中使用');
      return;
    }

    // 检查钉钉JSAPI是否已加载
    if (window.dd) {
      this.setupDingTalk();
    } else {
      // 如果钉钉JSAPI未加载，等待加载完成
      const checkInterval = setInterval(() => {
        if (window.dd) {
          clearInterval(checkInterval);
          this.setupDingTalk();
        }
      }, 100);

      // 10秒后停止检查
      setTimeout(() => {
        clearInterval(checkInterval);
        if (!window.dd) {
          console.warn('钉钉JSAPI加载超时，可能不在钉钉环境中');
        }
      }, 10000);
    }
  }

  /**
   * 设置钉钉JSAPI
   */
  setupDingTalk() {
    if (!window.dd) {
      console.warn('钉钉JSAPI未加载');
      return;
    }

    // 钉钉环境准备就绪
    window.dd.ready(() => {
      console.log('钉钉JSAPI初始化成功');
      this.isReady = true;
      
      // 执行所有等待的回调
      this.readyCallbacks.forEach(callback => {
        try {
          callback();
        } catch (error) {
          console.error('钉钉ready回调执行失败:', error);
        }
      });
      this.readyCallbacks = [];
    });

    // 钉钉环境错误处理
    window.dd.error((error) => {
      console.error('钉钉JSAPI错误:', error);
    });
  }

  /**
   * 检查是否在钉钉环境中
   * @returns {boolean}
   */
  isDingTalkEnvironment() {
    return !!(window.dd && window.dd.version) || DINGTALK_DETECTION.isDingTalkApp();
  }

  /**
   * 等待钉钉环境准备就绪
   * @param {Function} callback 回调函数
   */
  ready(callback) {
    if (this.isReady) {
      callback();
    } else {
      this.readyCallbacks.push(callback);
    }
  }

  /**
   * 扫码功能
   * @param {Object} options 扫码选项
   * @param {string} options.type 扫码类型，默认'qr'
   * @param {Function} options.onSuccess 成功回调
   * @param {Function} options.onFail 失败回调
   * @returns {Promise}
   */
  scan(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isDingTalkEnvironment()) {
        const error = new Error('不在钉钉环境中，无法使用扫码功能');
        if (options.onFail) options.onFail(error);
        reject(error);
        return;
      }

      this.ready(() => {
        window.dd.biz.util.scan({
          type: options.type || DINGTALK_CONFIG.SCAN_CONFIG.TYPE,
          onSuccess: (result) => {
            console.log('扫码成功:', result);
            if (options.onSuccess) options.onSuccess(result);
            resolve(result);
          },
          onFail: (error) => {
            console.error('扫码失败:', error);
            if (options.onFail) options.onFail(error);
            reject(error);
          }
        });
      });
    });
  }

  /**
   * 获取钉钉用户信息
   * @returns {Promise}
   */
  getUserInfo() {
    return new Promise((resolve, reject) => {
      if (!this.isDingTalkEnvironment()) {
        reject(new Error('不在钉钉环境中'));
        return;
      }

      this.ready(() => {
        window.dd.biz.user.get({
          onSuccess: (info) => {
            console.log('获取用户信息成功:', info);
            resolve(info);
          },
          onFail: (error) => {
            console.error('获取用户信息失败:', error);
            reject(error);
          }
        });
      });
    });
  }

  /**
   * 设置钉钉导航栏
   * @param {Object} options 导航栏选项
   */
  setNavigation(options = {}) {
    if (!this.isDingTalkEnvironment()) {
      return;
    }

    this.ready(() => {
      // 设置导航栏标题
      if (options.title) {
        window.dd.biz.navigation.setTitle({
          title: options.title,
          onSuccess: () => {
            console.log('设置导航栏标题成功');
          },
          onFail: (error) => {
            console.error('设置导航栏标题失败:', error);
          }
        });
      }

      // 设置右侧按钮
      if (options.rightButton) {
        window.dd.biz.navigation.setRight({
          show: true,
          control: true,
          text: options.rightButton.text || '更多',
          onSuccess: () => {
            console.log('设置导航栏右侧按钮成功');
          },
          onFail: (error) => {
            console.error('设置导航栏右侧按钮失败:', error);
          }
        });
      }
    });
  }

  /**
   * 显示钉钉提示框
   * @param {string} message 提示信息
   * @param {string} type 提示类型：success, error, info
   */
  showToast(message, type = 'info') {
    if (!this.isDingTalkEnvironment()) {
      // 如果不在钉钉环境中，使用浏览器原生提示
      alert(message);
      return;
    }

    this.ready(() => {
      const iconMap = {
        success: 'success',
        error: 'error',
        info: 'loading'
      };

      window.dd.device.notification.toast({
        icon: iconMap[type] || 'loading',
        text: message,
        duration: 2,
        onSuccess: () => {
          console.log('显示提示成功');
        },
        onFail: (error) => {
          console.error('显示提示失败:', error);
        }
      });
    });
  }

  /**
   * 获取钉钉设备信息
   * @returns {Promise}
   */
  getDeviceInfo() {
    return new Promise((resolve, reject) => {
      if (!this.isDingTalkEnvironment()) {
        // 返回浏览器信息
        resolve({
          platform: 'web',
          userAgent: navigator.userAgent,
          isDingTalk: false
        });
        return;
      }

      this.ready(() => {
        window.dd.device.base.getInterface({
          onSuccess: (data) => {
            resolve({
              ...data,
              isDingTalk: true
            });
          },
          onFail: (error) => {
            console.error('获取设备信息失败:', error);
            reject(error);
          }
        });
      });
    });
  }

  /**
   * 关闭当前页面
   */
  closePage() {
    if (!this.isDingTalkEnvironment()) {
      window.history.back();
      return;
    }

    this.ready(() => {
      window.dd.biz.navigation.close({
        onSuccess: () => {
          console.log('关闭页面成功');
        },
        onFail: (error) => {
          console.error('关闭页面失败:', error);
          window.history.back();
        }
      });
    });
  }
}

// 创建全局实例
const dingTalkUtils = new DingTalkUtils();

export default dingTalkUtils;
