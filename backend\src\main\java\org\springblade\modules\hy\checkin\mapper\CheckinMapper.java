/**
 * BladeX Commercial License Agreement
 * Copyright (c) 2018-2099, https://bladex.cn. All rights reserved.
 */
package org.springblade.modules.hy.checkin.mapper;

import org.springblade.modules.hy.checkin.pojo.entity.CheckinEntity;
import org.springblade.modules.hy.checkin.pojo.vo.CheckinVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 参会签到记录表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public interface CheckinMapper extends BaseMapper<CheckinEntity> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param checkin
	 * @return
	 */
	List<CheckinVO> selectCheckinPage(IPage page, CheckinVO checkin);

	/**
	 * 根据用户ID查询今日签到记录
	 *
	 * @param userId 用户ID
	 * @return 签到记录
	 */
	CheckinVO selectTodayCheckinByUserId(@Param("userId") Long userId);

	/**
	 * 根据用户ID和日期查询签到记录
	 *
	 * @param userId 用户ID
	 * @param date 日期
	 * @return 签到记录列表
	 */
	List<CheckinVO> selectCheckinByUserIdAndDate(@Param("userId") Long userId, @Param("date") String date);

	/**
	 * 统计今日签到人数
	 *
	 * @return 签到人数
	 */
	Integer countTodayCheckin();

	/**
	 * 统计指定日期签到人数
	 *
	 * @param date 日期
	 * @return 签到人数
	 */
	Integer countCheckinByDate(@Param("date") String date);

}
