<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.hy.checkin.mapper.CheckinMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="checkinResultMap" type="org.springblade.modules.hy.checkin.pojo.entity.CheckinEntity">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="checkin_time" property="checkinTime"/>
        <result column="checkin_type" property="checkinType"/>
        <result column="location" property="location"/>
        <result column="device_info" property="deviceInfo"/>
        <result column="qr_code_text" property="qrCodeText"/>
        <result column="checkin_status" property="checkinStatus"/>
        <result column="remark" property="remark"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <!-- 签到记录VO映射结果 -->
    <resultMap id="checkinVOResultMap" type="org.springblade.modules.hy.checkin.pojo.vo.CheckinVO" extends="checkinResultMap">
        <result column="user_name" property="userName"/>
        <result column="user_account" property="userAccount"/>
        <result column="user_phone" property="userPhone"/>
        <result column="user_email" property="userEmail"/>
        <result column="checkin_status_name" property="checkinStatusName"/>
        <result column="checkin_type_name" property="checkinTypeName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        id, user_id, checkin_time, checkin_type, location, device_info, qr_code_text, checkin_status, remark, create_user, create_dept, create_time, update_user, update_time, status, is_deleted
    </sql>

    <!-- 自定义分页查询 -->
    <select id="selectCheckinPage" resultMap="checkinVOResultMap">
        SELECT 
            c.id, c.user_id, c.checkin_time, c.checkin_type, c.location, c.device_info, 
            c.qr_code_text, c.checkin_status, c.remark, c.create_user, c.create_dept, 
            c.create_time, c.update_user, c.update_time, c.status, c.is_deleted,
            u.name as user_name, u.account as user_account, u.phone as user_phone, u.email as user_email,
            CASE c.checkin_status 
                WHEN 'success' THEN '签到成功'
                WHEN 'failed' THEN '签到失败'
                ELSE '未知状态'
            END as checkin_status_name,
            CASE c.checkin_type 
                WHEN 'qrcode' THEN '扫码签到'
                WHEN 'manual' THEN '手动签到'
                ELSE '其他方式'
            END as checkin_type_name
        FROM hy_checkin c
        LEFT JOIN blade_user u ON c.user_id = u.id
        WHERE c.is_deleted = 0
        <if test="checkin.userId != null">
            AND c.user_id = #{checkin.userId}
        </if>
        <if test="checkin.checkinType != null and checkin.checkinType != ''">
            AND c.checkin_type = #{checkin.checkinType}
        </if>
        <if test="checkin.checkinStatus != null and checkin.checkinStatus != ''">
            AND c.checkin_status = #{checkin.checkinStatus}
        </if>
        <if test="checkin.location != null and checkin.location != ''">
            AND c.location LIKE CONCAT('%', #{checkin.location}, '%')
        </if>
        ORDER BY c.checkin_time DESC
    </select>

    <!-- 根据用户ID查询今日签到记录 -->
    <select id="selectTodayCheckinByUserId" resultMap="checkinVOResultMap">
        SELECT 
            c.id, c.user_id, c.checkin_time, c.checkin_type, c.location, c.device_info, 
            c.qr_code_text, c.checkin_status, c.remark, c.create_user, c.create_dept, 
            c.create_time, c.update_user, c.update_time, c.status, c.is_deleted,
            u.name as user_name, u.account as user_account, u.phone as user_phone, u.email as user_email,
            CASE c.checkin_status 
                WHEN 'success' THEN '签到成功'
                WHEN 'failed' THEN '签到失败'
                ELSE '未知状态'
            END as checkin_status_name,
            CASE c.checkin_type 
                WHEN 'qrcode' THEN '扫码签到'
                WHEN 'manual' THEN '手动签到'
                ELSE '其他方式'
            END as checkin_type_name
        FROM hy_checkin c
        LEFT JOIN blade_user u ON c.user_id = u.id
        WHERE c.is_deleted = 0 
            AND c.user_id = #{userId}
            AND DATE(c.checkin_time) = CURDATE()
            AND c.checkin_status = 'success'
        ORDER BY c.checkin_time DESC
        LIMIT 1
    </select>

    <!-- 根据用户ID和日期查询签到记录 -->
    <select id="selectCheckinByUserIdAndDate" resultMap="checkinVOResultMap">
        SELECT 
            c.id, c.user_id, c.checkin_time, c.checkin_type, c.location, c.device_info, 
            c.qr_code_text, c.checkin_status, c.remark, c.create_user, c.create_dept, 
            c.create_time, c.update_user, c.update_time, c.status, c.is_deleted,
            u.name as user_name, u.account as user_account, u.phone as user_phone, u.email as user_email
        FROM hy_checkin c
        LEFT JOIN blade_user u ON c.user_id = u.id
        WHERE c.is_deleted = 0 
            AND c.user_id = #{userId}
            AND DATE(c.checkin_time) = #{date}
        ORDER BY c.checkin_time DESC
    </select>

    <!-- 统计今日签到人数 -->
    <select id="countTodayCheckin" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT user_id)
        FROM hy_checkin
        WHERE is_deleted = 0 
            AND DATE(checkin_time) = CURDATE()
            AND checkin_status = 'success'
    </select>

    <!-- 统计指定日期签到人数 -->
    <select id="countCheckinByDate" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT user_id)
        FROM hy_checkin
        WHERE is_deleted = 0 
            AND DATE(checkin_time) = #{date}
            AND checkin_status = 'success'
    </select>

</mapper>
