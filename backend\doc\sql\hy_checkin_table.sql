-- 创建签到记录表
-- 如果表已存在则删除
DROP TABLE IF EXISTS "hy_checkin";

-- 创建新的签到记录表
CREATE TABLE hy_checkin (
    id BIGSERIAL PRIMARY KEY,                    -- 主键，自增
    user_id BIGINT NOT NULL,                     -- 用户ID，关联blade_user表
    checkin_time TIMESTAMP NOT NULL,             -- 签到时间
    checkin_type VARCHAR(20) DEFAULT 'qrcode',   -- 签到类型：qrcode-扫码签到，manual-手动签到
    location VARCHAR(100),                       -- 签到位置
    device_info TEXT,                            -- 设备信息
    qr_code_text TEXT,                           -- 二维码内容
    checkin_status VARCHAR(20) DEFAULT 'success', -- 签到状态：success-成功，failed-失败
    remark TEXT,                                 -- 备注
    create_user BIGINT,                          -- 创建人
    create_dept BIGINT,                          -- 创建部门
    create_time TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP, -- 创建时间
    update_user BIGINT,                          -- 更新人
    update_time TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP, -- 更新时间
    status INT DEFAULT 1,                        -- 状态：1-正常，0-禁用
    is_deleted INT DEFAULT 0                     -- 是否删除：0-未删除，1-已删除
);

-- 添加表注释
COMMENT ON TABLE hy_checkin IS '会议签到记录表';

-- 添加字段注释
COMMENT ON COLUMN hy_checkin.id IS '主键，自增';
COMMENT ON COLUMN hy_checkin.user_id IS '用户ID，关联blade_user表';
COMMENT ON COLUMN hy_checkin.checkin_time IS '签到时间';
COMMENT ON COLUMN hy_checkin.checkin_type IS '签到类型：qrcode-扫码签到，manual-手动签到';
COMMENT ON COLUMN hy_checkin.location IS '签到位置';
COMMENT ON COLUMN hy_checkin.device_info IS '设备信息';
COMMENT ON COLUMN hy_checkin.qr_code_text IS '二维码内容';
COMMENT ON COLUMN hy_checkin.checkin_status IS '签到状态：success-成功，failed-失败';
COMMENT ON COLUMN hy_checkin.remark IS '备注';
COMMENT ON COLUMN hy_checkin.create_user IS '创建人';
COMMENT ON COLUMN hy_checkin.create_dept IS '创建部门';
COMMENT ON COLUMN hy_checkin.create_time IS '创建时间';
COMMENT ON COLUMN hy_checkin.update_user IS '更新人';
COMMENT ON COLUMN hy_checkin.update_time IS '更新时间';
COMMENT ON COLUMN hy_checkin.status IS '状态：1-正常，0-禁用';
COMMENT ON COLUMN hy_checkin.is_deleted IS '是否删除：0-未删除，1-已删除';

-- 创建索引
CREATE INDEX idx_hy_checkin_user_id ON hy_checkin(user_id);
CREATE INDEX idx_hy_checkin_checkin_time ON hy_checkin(checkin_time);
CREATE INDEX idx_hy_checkin_checkin_type ON hy_checkin(checkin_type);
CREATE INDEX idx_hy_checkin_status ON hy_checkin(checkin_status);
CREATE INDEX idx_hy_checkin_is_deleted ON hy_checkin(is_deleted);

-- 插入测试数据
INSERT INTO hy_checkin (user_id, checkin_time, checkin_type, location, device_info, qr_code_text, checkin_status, remark, create_user, create_dept, create_time, update_user, update_time, status, is_deleted) VALUES
(1123598821738675201, '2025-08-04 08:35:00', 'qrcode', '主会场大厅', 'iPhone 13 Pro', 'conference_checkin_20250804_001', 'success', '扫码签到成功', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(1123598821738675202, '2025-08-04 08:42:00', 'manual', '主会场大厅', 'Android Phone', '', 'success', '手动签到', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0),
(1123598821738675203, '2025-08-04 08:38:00', 'qrcode', '主会场大厅', 'iPad Pro', 'conference_checkin_20250804_002', 'success', '扫码签到成功', 1123598821738675201, 1123598813738675201, NOW(), 1123598821738675201, NOW(), 1, 0);
