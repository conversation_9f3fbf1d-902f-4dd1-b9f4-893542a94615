<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会议签到二维码生成器</title>
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-bottom: 20px;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        h2 {
            color: #34495e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        input, select, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #bdc3c7;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 10px rgba(52, 152, 219, 0.3);
        }
        button {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            margin: 5px;
        }
        button:hover {
            background: linear-gradient(135deg, #2980b9, #1f4e79);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .qr-container {
            text-align: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            margin-top: 20px;
        }
        #qrcode {
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            display: inline-block;
        }
        .qr-info {
            background: #e8f4fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            border-left: 4px solid #3498db;
        }
        .preset-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        .preset-btn {
            background: linear-gradient(135deg, #27ae60, #229954);
            font-size: 14px;
            padding: 8px 15px;
        }
        .preset-btn:hover {
            background: linear-gradient(135deg, #229954, #1e8449);
        }
        .copy-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            font-size: 14px;
            padding: 8px 15px;
        }
        .copy-btn:hover {
            background: linear-gradient(135deg, #c0392b, #a93226);
        }
        .download-btn {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }
        .download-btn:hover {
            background: linear-gradient(135deg, #e67e22, #d35400);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 会议签到二维码生成器</h1>
        
        <div class="form-group">
            <label for="qrContent">二维码内容:</label>
            <textarea id="qrContent" rows="3" placeholder="输入二维码内容，例如：conference_checkin_20250804_001">conference_checkin_20250804_001</textarea>
            
            <div class="preset-buttons">
                <button class="preset-btn" onclick="setPresetContent('main')">主会场签到</button>
                <button class="preset-btn" onclick="setPresetContent('sub1')">分会场A签到</button>
                <button class="preset-btn" onclick="setPresetContent('sub2')">分会场B签到</button>
                <button class="preset-btn" onclick="setPresetContent('vip')">VIP签到</button>
                <button class="preset-btn" onclick="setPresetContent('morning')">上午场签到</button>
                <button class="preset-btn" onclick="setPresetContent('afternoon')">下午场签到</button>
            </div>
        </div>

        <div class="form-group">
            <label for="qrSize">二维码大小:</label>
            <select id="qrSize">
                <option value="200">小 (200x200)</option>
                <option value="300" selected>中 (300x300)</option>
                <option value="400">大 (400x400)</option>
                <option value="500">超大 (500x500)</option>
            </select>
        </div>

        <div class="form-group">
            <label for="errorLevel">容错级别:</label>
            <select id="errorLevel">
                <option value="L">低 (L) - 约7%</option>
                <option value="M" selected>中 (M) - 约15%</option>
                <option value="Q">高 (Q) - 约25%</option>
                <option value="H">最高 (H) - 约30%</option>
            </select>
        </div>

        <button onclick="generateQRCode()">🎨 生成二维码</button>
        <button class="copy-btn" onclick="copyContent()">📋 复制内容</button>
        <button class="download-btn" onclick="downloadQRCode()">💾 下载二维码</button>

        <div class="qr-container" id="qrContainer" style="display: none;">
            <h2>📱 生成的签到二维码</h2>
            <div id="qrcode"></div>
            <div class="qr-info">
                <strong>二维码内容:</strong> <span id="qrContentDisplay"></span><br>
                <strong>生成时间:</strong> <span id="generateTime"></span><br>
                <strong>使用说明:</strong> 请在会议签到系统中扫描此二维码进行签到
            </div>
        </div>
    </div>

    <script>
        let currentQRCodeDataURL = null;

        // 预设内容模板
        const presetContents = {
            main: 'conference_checkin_main_hall_' + getCurrentDateString(),
            sub1: 'conference_checkin_hall_a_' + getCurrentDateString(),
            sub2: 'conference_checkin_hall_b_' + getCurrentDateString(),
            vip: 'conference_checkin_vip_' + getCurrentDateString(),
            morning: 'conference_checkin_morning_' + getCurrentDateString(),
            afternoon: 'conference_checkin_afternoon_' + getCurrentDateString()
        };

        function getCurrentDateString() {
            const now = new Date();
            return now.getFullYear() + 
                   String(now.getMonth() + 1).padStart(2, '0') + 
                   String(now.getDate()).padStart(2, '0') + '_' +
                   String(now.getHours()).padStart(2, '0') + 
                   String(now.getMinutes()).padStart(2, '0');
        }

        function setPresetContent(type) {
            document.getElementById('qrContent').value = presetContents[type];
        }

        function generateQRCode() {
            const content = document.getElementById('qrContent').value.trim();
            const size = parseInt(document.getElementById('qrSize').value);
            const errorLevel = document.getElementById('errorLevel').value;

            if (!content) {
                alert('请输入二维码内容！');
                return;
            }

            // 清空之前的二维码
            document.getElementById('qrcode').innerHTML = '';

            // 生成二维码
            QRCode.toCanvas(document.getElementById('qrcode'), content, {
                width: size,
                height: size,
                errorCorrectionLevel: errorLevel,
                color: {
                    dark: '#2c3e50',
                    light: '#ffffff'
                },
                margin: 2
            }, function (error, canvas) {
                if (error) {
                    console.error('生成二维码失败:', error);
                    alert('生成二维码失败，请检查输入内容！');
                    return;
                }

                // 保存二维码数据URL用于下载
                currentQRCodeDataURL = canvas.toDataURL();

                // 显示二维码容器
                document.getElementById('qrContainer').style.display = 'block';
                document.getElementById('qrContentDisplay').textContent = content;
                document.getElementById('generateTime').textContent = new Date().toLocaleString();

                // 滚动到二维码位置
                document.getElementById('qrContainer').scrollIntoView({ behavior: 'smooth' });
            });
        }

        function copyContent() {
            const content = document.getElementById('qrContent').value;
            if (!content) {
                alert('没有内容可复制！');
                return;
            }

            navigator.clipboard.writeText(content).then(() => {
                alert('二维码内容已复制到剪贴板！');
            }).catch(err => {
                console.error('复制失败:', err);
                alert('复制失败，请手动复制内容！');
            });
        }

        function downloadQRCode() {
            if (!currentQRCodeDataURL) {
                alert('请先生成二维码！');
                return;
            }

            const link = document.createElement('a');
            link.download = '会议签到二维码_' + getCurrentDateString() + '.png';
            link.href = currentQRCodeDataURL;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // 页面加载完成后自动生成一个示例二维码
        document.addEventListener('DOMContentLoaded', function() {
            console.log('二维码生成器已加载');
        });
    </script>
</body>
</html>
