<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>签到API测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        label {
            font-weight: bold;
            display: block;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>扫码签到API测试页面</h1>
    
    <div class="container">
        <h2>1. 验证二维码</h2>
        <label for="qrCodeText">二维码内容:</label>
        <input type="text" id="qrCodeText" value="conference_checkin_20250804_001" placeholder="输入二维码内容">
        <button onclick="validateQRCode()">验证二维码</button>
        <div id="validateResult" class="result"></div>
    </div>

    <div class="container">
        <h2>2. 提交签到记录</h2>
        <label for="checkinQRCode">二维码内容:</label>
        <input type="text" id="checkinQRCode" value="conference_checkin_20250804_002" placeholder="签到二维码内容">
        
        <label for="checkinLocation">签到位置:</label>
        <input type="text" id="checkinLocation" value="主会场大厅" placeholder="签到位置">
        
        <label for="checkinRemark">备注:</label>
        <textarea id="checkinRemark" placeholder="签到备注">扫码签到测试</textarea>
        
        <button onclick="submitCheckin()">提交签到</button>
        <div id="checkinResult" class="result"></div>
    </div>

    <div class="container">
        <h2>3. 查询今日签到状态</h2>
        <button onclick="getTodayStatus()">查询今日签到状态</button>
        <div id="statusResult" class="result"></div>
    </div>

    <div class="container">
        <h2>4. 统计今日签到人数</h2>
        <button onclick="getTodayCount()">统计今日签到人数</button>
        <div id="countResult" class="result"></div>
    </div>

    <div class="container">
        <h2>5. 查询签到记录列表</h2>
        <button onclick="getCheckinList()">查询签到记录</button>
        <div id="listResult" class="result"></div>
    </div>

    <script>
        // API基础URL（需要根据实际情况修改）
        const API_BASE_URL = '/hy/checkin';
        
        // 通用请求函数
        async function apiRequest(url, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                        // 这里需要添加实际的认证头
                        // 'Authorization': 'Bearer your-token'
                    }
                };
                
                if (data) {
                    options.body = JSON.stringify(data);
                }
                
                const response = await fetch(url, options);
                const result = await response.json();
                return result;
            } catch (error) {
                return { error: error.message };
            }
        }

        // 验证二维码
        async function validateQRCode() {
            const qrCodeText = document.getElementById('qrCodeText').value;
            const resultDiv = document.getElementById('validateResult');
            
            if (!qrCodeText) {
                resultDiv.textContent = '请输入二维码内容';
                resultDiv.className = 'result error';
                return;
            }
            
            const result = await apiRequest(`${API_BASE_URL}/validate-qrcode`, 'POST', {
                qrCodeText: qrCodeText
            });
            
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        // 提交签到
        async function submitCheckin() {
            const qrCodeText = document.getElementById('checkinQRCode').value;
            const location = document.getElementById('checkinLocation').value;
            const remark = document.getElementById('checkinRemark').value;
            const resultDiv = document.getElementById('checkinResult');
            
            if (!qrCodeText) {
                resultDiv.textContent = '请输入二维码内容';
                resultDiv.className = 'result error';
                return;
            }
            
            const checkinData = {
                qrCodeText: qrCodeText,
                checkinTime: new Date().toISOString(),
                checkinType: 'qrcode',
                location: location || '会议现场',
                deviceInfo: navigator.userAgent,
                remark: remark
            };
            
            const result = await apiRequest(`${API_BASE_URL}/submit`, 'POST', checkinData);
            
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        // 查询今日签到状态
        async function getTodayStatus() {
            const resultDiv = document.getElementById('statusResult');
            
            const result = await apiRequest(`${API_BASE_URL}/today-status`);
            
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        // 统计今日签到人数
        async function getTodayCount() {
            const resultDiv = document.getElementById('countResult');
            
            const result = await apiRequest(`${API_BASE_URL}/today-count`);
            
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        // 查询签到记录列表
        async function getCheckinList() {
            const resultDiv = document.getElementById('listResult');
            
            const result = await apiRequest(`${API_BASE_URL}/list?current=1&size=10`);
            
            resultDiv.textContent = JSON.stringify(result, null, 2);
            resultDiv.className = result.success ? 'result success' : 'result error';
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('签到API测试页面已加载');
            console.log('请确保后端服务已启动，并且已正确配置API基础URL');
        });
    </script>
</body>
</html>
