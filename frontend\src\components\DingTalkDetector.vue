<template>
  <div class="dingtalk-detector" v-if="showDetector">
    <div class="detector-overlay" @click="hideDetector"></div>
    <div class="detector-content">
      <div class="detector-header">
        <i class="fas fa-mobile-alt"></i>
        <h3>环境检测</h3>
      </div>
      
      <div class="detector-body">
        <div class="env-info">
          <div class="info-item">
            <span class="label">当前环境:</span>
            <span class="value" :class="envClass">{{ envText }}</span>
          </div>
          
          <div class="info-item" v-if="dingTalkVersion">
            <span class="label">钉钉版本:</span>
            <span class="value">{{ dingTalkVersion }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">设备类型:</span>
            <span class="value">{{ deviceType }}</span>
          </div>
          
          <div class="info-item">
            <span class="label">JSAPI状态:</span>
            <span class="value" :class="jsapiClass">{{ jsapiStatus }}</span>
          </div>
        </div>
        
        <div class="features-status">
          <h4>功能支持状态</h4>
          <div class="feature-list">
            <div class="feature-item" v-for="feature in features" :key="feature.name">
              <i :class="feature.available ? 'fas fa-check-circle success' : 'fas fa-times-circle error'"></i>
              <span>{{ feature.name }}</span>
            </div>
          </div>
        </div>
        
        <div class="recommendations" v-if="!isDingTalkEnv">
          <h4>建议</h4>
          <ul>
            <li>为了获得最佳体验，请在钉钉客户端中打开此页面</li>
            <li>扫码签到功能需要在钉钉环境中使用</li>
            <li>当前环境下将提供模拟功能用于测试</li>
          </ul>
        </div>
      </div>
      
      <div class="detector-footer">
        <button class="btn-primary" @click="hideDetector">我知道了</button>
        <button class="btn-secondary" @click="testFeatures" v-if="isDingTalkEnv">测试功能</button>
      </div>
    </div>
  </div>
</template>

<script>
import { DINGTALK_DETECTION } from '@/config/dingtalk'

export default {
  name: 'DingTalkDetector',
  data() {
    return {
      showDetector: false,
      isDingTalkEnv: false,
      dingTalkVersion: null,
      jsapiReady: false,
      features: [
        { name: '扫码功能', available: false },
        { name: '用户信息获取', available: false },
        { name: '设备信息获取', available: false },
        { name: '导航栏控制', available: false }
      ]
    }
  },
  computed: {
    envText() {
      if (this.isDingTalkEnv) {
        if (DINGTALK_DETECTION.isDingTalkMobile()) {
          return '钉钉移动端'
        } else if (DINGTALK_DETECTION.isDingTalkPC()) {
          return '钉钉PC端'
        } else {
          return '钉钉环境'
        }
      } else {
        return '浏览器环境'
      }
    },
    envClass() {
      return this.isDingTalkEnv ? 'success' : 'warning'
    },
    deviceType() {
      const ua = navigator.userAgent.toLowerCase()
      if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
        return '移动设备'
      } else {
        return '桌面设备'
      }
    },
    jsapiStatus() {
      if (this.jsapiReady) {
        return 'JSAPI已就绪'
      } else if (window.dd) {
        return 'JSAPI已加载'
      } else {
        return 'JSAPI未加载'
      }
    },
    jsapiClass() {
      if (this.jsapiReady) {
        return 'success'
      } else if (window.dd) {
        return 'warning'
      } else {
        return 'error'
      }
    }
  },
  mounted() {
    this.detectEnvironment()
  },
  methods: {
    /**
     * 检测环境
     */
    detectEnvironment() {
      this.isDingTalkEnv = DINGTALK_DETECTION.isDingTalkApp()
      this.dingTalkVersion = DINGTALK_DETECTION.getDingTalkVersion()
      
      if (this.isDingTalkEnv && window.dd) {
        this.checkJSAPIReady()
      }
      
      this.updateFeatureStatus()
    },
    
    /**
     * 检查JSAPI是否就绪
     */
    checkJSAPIReady() {
      if (this.$dingTalk) {
        this.$dingTalk.ready(() => {
          this.jsapiReady = true
          this.updateFeatureStatus()
        })
      }
    },
    
    /**
     * 更新功能状态
     */
    updateFeatureStatus() {
      this.features = [
        { 
          name: '扫码功能', 
          available: this.isDingTalkEnv && this.jsapiReady 
        },
        { 
          name: '用户信息获取', 
          available: this.isDingTalkEnv && this.jsapiReady 
        },
        { 
          name: '设备信息获取', 
          available: this.isDingTalkEnv && this.jsapiReady 
        },
        { 
          name: '导航栏控制', 
          available: this.isDingTalkEnv && this.jsapiReady 
        }
      ]
    },
    
    /**
     * 显示检测器
     */
    show() {
      this.detectEnvironment()
      this.showDetector = true
    },
    
    /**
     * 隐藏检测器
     */
    hideDetector() {
      this.showDetector = false
    },
    
    /**
     * 测试功能
     */
    async testFeatures() {
      if (!this.isDingTalkEnv) {
        alert('请在钉钉环境中测试功能')
        return
      }
      
      try {
        // 测试获取用户信息
        const userInfo = await this.$dingTalk.getUserInfo()
        console.log('用户信息:', userInfo)
        
        // 测试获取设备信息
        const deviceInfo = await this.$dingTalk.getDeviceInfo()
        console.log('设备信息:', deviceInfo)
        
        this.$dingTalk.showToast('功能测试完成，请查看控制台', 'success')
      } catch (error) {
        console.error('功能测试失败:', error)
        this.$dingTalk.showToast('功能测试失败', 'error')
      }
    }
  }
}
</script>

<style scoped>
.dingtalk-detector {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.detector-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.detector-content {
  position: relative;
  background: white;
  border-radius: 15px;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.detector-header {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.detector-header i {
  font-size: 24px;
  color: #4682B4;
  margin-bottom: 10px;
}

.detector-header h3 {
  margin: 0;
  color: #333;
}

.detector-body {
  padding: 20px;
}

.env-info {
  margin-bottom: 20px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #666;
}

.value {
  font-weight: 600;
}

.value.success {
  color: #28a745;
}

.value.warning {
  color: #ffc107;
}

.value.error {
  color: #dc3545;
}

.features-status h4,
.recommendations h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.feature-item i.success {
  color: #28a745;
}

.feature-item i.error {
  color: #dc3545;
}

.recommendations {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.recommendations li {
  margin-bottom: 5px;
  color: #666;
  font-size: 14px;
}

.detector-footer {
  padding: 20px;
  border-top: 1px solid #eee;
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.btn-primary,
.btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.btn-primary {
  background: #4682B4;
  color: white;
}

.btn-primary:hover {
  background: #3a6fa5;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}
</style>
