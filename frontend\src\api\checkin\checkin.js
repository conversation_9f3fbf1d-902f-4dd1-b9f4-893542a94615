import request from '@/axios';

/**
 * 提交签到记录
 * @param {Object} checkinData 签到数据
 * @returns {Promise}
 */
export const submitCheckin = (checkinData) => {
  return request({
    url: '/hy/checkin/submit',
    method: 'post',
    data: checkinData
  });
};

/**
 * 获取用户签到记录列表
 * @param {number} current 当前页
 * @param {number} size 页面大小
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export const getCheckinList = (current, size, params) => {
  return request({
    url: '/hy/checkin/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  });
};

/**
 * 获取签到详情
 * @param {number} id 签到记录ID
 * @returns {Promise}
 */
export const getCheckinDetail = (id) => {
  return request({
    url: '/hy/checkin/detail',
    method: 'get',
    params: {
      id
    }
  });
};

/**
 * 获取当前用户今日签到状态
 * @returns {Promise}
 */
export const getTodayCheckinStatus = () => {
  return request({
    url: '/hy/checkin/today-status',
    method: 'get'
  });
};

/**
 * 验证二维码签到
 * @param {string} qrCodeText 二维码内容
 * @returns {Promise}
 */
export const validateQRCodeCheckin = (qrCodeText) => {
  return request({
    url: '/hy/checkin/validate-qrcode',
    method: 'post',
    data: {
      qrCodeText
    }
  });
};
