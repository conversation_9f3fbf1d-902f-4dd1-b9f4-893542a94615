<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉JSAPI集成演示</title>
    <!-- 引入钉钉JSAPI -->
    <script src="https://g.alicdn.com/dingding/open-develop/1.9.0/dingtalk.js"></script>
    <style>
        body {
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #87CEEB 0%, #4682B4 50%, #1E90FF 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #4682B4;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-label {
            font-weight: 600;
            color: #495057;
        }
        
        .status-value {
            font-weight: 500;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
        }
        
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .function-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        
        .function-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .function-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            border-color: #4682B4;
        }
        
        .function-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .function-card i {
            color: #4682B4;
            font-size: 20px;
        }
        
        .function-card p {
            color: #6c757d;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .btn {
            background: #4682B4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s;
            width: 100%;
        }
        
        .btn:hover {
            background: #3a6fa5;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }
        
        .result-area {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            border: 1px solid #dee2e6;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .alert-info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        
        .alert-warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        
        .icon-success { color: #28a745; }
        .icon-warning { color: #ffc107; }
        .icon-error { color: #dc3545; }
    </style>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container">
        <h1>🔧 钉钉JSAPI集成演示</h1>
        
        <!-- 环境检测状态 -->
        <div class="status-card">
            <h3><i class="fas fa-info-circle"></i> 环境检测状态</h3>
            <div class="status-item">
                <span class="status-label">当前环境:</span>
                <span id="envStatus" class="status-value">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">钉钉JSAPI:</span>
                <span id="jsapiStatus" class="status-value">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">设备类型:</span>
                <span id="deviceType" class="status-value">检测中...</span>
            </div>
            <div class="status-item">
                <span class="status-label">钉钉版本:</span>
                <span id="dingVersion" class="status-value">检测中...</span>
            </div>
        </div>
        
        <!-- 提示信息 -->
        <div id="alertContainer"></div>
        
        <!-- 功能测试区域 -->
        <div class="function-grid">
            <!-- 扫码功能 -->
            <div class="function-card">
                <h3><i class="fas fa-qrcode"></i> 扫码功能</h3>
                <p>测试钉钉扫码API，扫描二维码获取内容</p>
                <button class="btn" onclick="testScan()" id="scanBtn">
                    <i class="fas fa-camera"></i> 开始扫码
                </button>
                <div id="scanResult" class="result-area" style="display: none;"></div>
            </div>
            
            <!-- 用户信息 -->
            <div class="function-card">
                <h3><i class="fas fa-user"></i> 用户信息</h3>
                <p>获取当前钉钉用户的基本信息</p>
                <button class="btn" onclick="testUserInfo()" id="userBtn">
                    <i class="fas fa-id-card"></i> 获取用户信息
                </button>
                <div id="userResult" class="result-area" style="display: none;"></div>
            </div>
            
            <!-- 设备信息 -->
            <div class="function-card">
                <h3><i class="fas fa-mobile-alt"></i> 设备信息</h3>
                <p>获取当前设备的详细信息</p>
                <button class="btn" onclick="testDeviceInfo()" id="deviceBtn">
                    <i class="fas fa-info"></i> 获取设备信息
                </button>
                <div id="deviceResult" class="result-area" style="display: none;"></div>
            </div>
            
            <!-- 导航栏控制 -->
            <div class="function-card">
                <h3><i class="fas fa-bars"></i> 导航栏控制</h3>
                <p>测试钉钉导航栏标题设置功能</p>
                <button class="btn" onclick="testNavigation()" id="navBtn">
                    <i class="fas fa-edit"></i> 设置导航栏
                </button>
                <div id="navResult" class="result-area" style="display: none;"></div>
            </div>
            
            <!-- 提示功能 -->
            <div class="function-card">
                <h3><i class="fas fa-bell"></i> 提示功能</h3>
                <p>测试钉钉原生提示框功能</p>
                <button class="btn" onclick="testToast()" id="toastBtn">
                    <i class="fas fa-comment"></i> 显示提示
                </button>
                <div id="toastResult" class="result-area" style="display: none;"></div>
            </div>
            
            <!-- 签到模拟 -->
            <div class="function-card">
                <h3><i class="fas fa-check-circle"></i> 签到模拟</h3>
                <p>模拟完整的扫码签到流程</p>
                <button class="btn" onclick="testCheckin()" id="checkinBtn">
                    <i class="fas fa-clipboard-check"></i> 模拟签到
                </button>
                <div id="checkinResult" class="result-area" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        let isDingTalkEnv = false;
        let isJSAPIReady = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            detectEnvironment();
            initDingTalk();
        });

        // 环境检测
        function detectEnvironment() {
            const ua = navigator.userAgent.toLowerCase();
            isDingTalkEnv = ua.includes('dingtalk');
            
            // 更新环境状态
            const envStatus = document.getElementById('envStatus');
            if (isDingTalkEnv) {
                envStatus.textContent = '钉钉环境';
                envStatus.className = 'status-value status-success';
            } else {
                envStatus.textContent = '浏览器环境';
                envStatus.className = 'status-value status-warning';
            }
            
            // 设备类型
            const deviceType = document.getElementById('deviceType');
            if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
                deviceType.textContent = '移动设备';
            } else {
                deviceType.textContent = '桌面设备';
            }
            deviceType.className = 'status-value status-success';
            
            // 钉钉版本
            const dingVersion = document.getElementById('dingVersion');
            const versionMatch = navigator.userAgent.match(/DingTalk\/(\d+\.\d+\.\d+)/);
            if (versionMatch) {
                dingVersion.textContent = versionMatch[1];
                dingVersion.className = 'status-value status-success';
            } else {
                dingVersion.textContent = '未检测到';
                dingVersion.className = 'status-value status-error';
            }
            
            // 显示提示信息
            showAlert();
        }

        // 初始化钉钉JSAPI
        function initDingTalk() {
            const jsapiStatus = document.getElementById('jsapiStatus');
            
            if (typeof dd !== 'undefined') {
                jsapiStatus.textContent = 'JSAPI已加载';
                jsapiStatus.className = 'status-value status-warning';
                
                if (isDingTalkEnv) {
                    dd.ready(() => {
                        isJSAPIReady = true;
                        jsapiStatus.textContent = 'JSAPI已就绪';
                        jsapiStatus.className = 'status-value status-success';
                        enableButtons();
                    });
                    
                    dd.error((error) => {
                        console.error('钉钉JSAPI错误:', error);
                        jsapiStatus.textContent = 'JSAPI错误';
                        jsapiStatus.className = 'status-value status-error';
                    });
                } else {
                    enableButtons();
                }
            } else {
                jsapiStatus.textContent = 'JSAPI未加载';
                jsapiStatus.className = 'status-value status-error';
            }
        }

        // 显示提示信息
        function showAlert() {
            const alertContainer = document.getElementById('alertContainer');
            let alertHTML = '';
            
            if (!isDingTalkEnv) {
                alertHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>注意：</strong> 当前不在钉钉环境中，部分功能将使用模拟数据进行演示。
                        为了体验完整功能，请在钉钉客户端中打开此页面。
                    </div>
                `;
            } else {
                alertHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>太好了！</strong> 检测到您在钉钉环境中，所有功能都可以正常使用。
                    </div>
                `;
            }
            
            alertContainer.innerHTML = alertHTML;
        }

        // 启用按钮
        function enableButtons() {
            const buttons = document.querySelectorAll('.btn');
            buttons.forEach(btn => {
                btn.disabled = false;
            });
        }

        // 测试扫码功能
        function testScan() {
            const resultDiv = document.getElementById('scanResult');
            resultDiv.style.display = 'block';
            
            if (isDingTalkEnv && isJSAPIReady) {
                resultDiv.textContent = '正在启动扫码...';
                
                dd.biz.util.scan({
                    type: "qr",
                    onSuccess: (result) => {
                        resultDiv.textContent = `扫码成功！\n二维码内容: ${result.text}`;
                    },
                    onFail: (error) => {
                        resultDiv.textContent = `扫码失败: ${JSON.stringify(error, null, 2)}`;
                    }
                });
            } else {
                // 模拟扫码结果
                setTimeout(() => {
                    resultDiv.textContent = `模拟扫码成功！\n二维码内容: conference_checkin_${Date.now()}`;
                }, 1000);
            }
        }

        // 测试用户信息获取
        function testUserInfo() {
            const resultDiv = document.getElementById('userResult');
            resultDiv.style.display = 'block';
            
            if (isDingTalkEnv && isJSAPIReady) {
                resultDiv.textContent = '正在获取用户信息...';
                
                dd.biz.user.get({
                    onSuccess: (info) => {
                        resultDiv.textContent = `用户信息获取成功！\n${JSON.stringify(info, null, 2)}`;
                    },
                    onFail: (error) => {
                        resultDiv.textContent = `获取用户信息失败: ${JSON.stringify(error, null, 2)}`;
                    }
                });
            } else {
                // 模拟用户信息
                setTimeout(() => {
                    const mockUser = {
                        name: '张三',
                        userId: 'mock_user_123',
                        avatar: 'https://example.com/avatar.jpg'
                    };
                    resultDiv.textContent = `模拟用户信息：\n${JSON.stringify(mockUser, null, 2)}`;
                }, 1000);
            }
        }

        // 测试设备信息获取
        function testDeviceInfo() {
            const resultDiv = document.getElementById('deviceResult');
            resultDiv.style.display = 'block';
            
            const deviceInfo = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                screenWidth: screen.width,
                screenHeight: screen.height,
                isDingTalk: isDingTalkEnv,
                timestamp: new Date().toISOString()
            };
            
            resultDiv.textContent = `设备信息：\n${JSON.stringify(deviceInfo, null, 2)}`;
        }

        // 测试导航栏控制
        function testNavigation() {
            const resultDiv = document.getElementById('navResult');
            resultDiv.style.display = 'block';
            
            if (isDingTalkEnv && isJSAPIReady) {
                dd.biz.navigation.setTitle({
                    title: '钉钉集成演示',
                    onSuccess: () => {
                        resultDiv.textContent = '导航栏标题设置成功！';
                    },
                    onFail: (error) => {
                        resultDiv.textContent = `设置导航栏失败: ${JSON.stringify(error, null, 2)}`;
                    }
                });
            } else {
                resultDiv.textContent = '模拟设置导航栏标题成功！\n标题: 钉钉集成演示';
            }
        }

        // 测试提示功能
        function testToast() {
            const resultDiv = document.getElementById('toastResult');
            resultDiv.style.display = 'block';
            
            if (isDingTalkEnv && isJSAPIReady) {
                dd.device.notification.toast({
                    icon: 'success',
                    text: '这是一个钉钉原生提示！',
                    duration: 2,
                    onSuccess: () => {
                        resultDiv.textContent = '钉钉原生提示显示成功！';
                    },
                    onFail: (error) => {
                        resultDiv.textContent = `显示提示失败: ${JSON.stringify(error, null, 2)}`;
                    }
                });
            } else {
                alert('这是一个浏览器原生提示！');
                resultDiv.textContent = '浏览器原生提示显示成功！';
            }
        }

        // 测试签到流程
        function testCheckin() {
            const resultDiv = document.getElementById('checkinResult');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '开始模拟签到流程...\n';
            
            // 模拟签到步骤
            setTimeout(() => {
                resultDiv.textContent += '1. 环境检测完成 ✓\n';
            }, 500);
            
            setTimeout(() => {
                resultDiv.textContent += '2. 启动扫码功能 ✓\n';
            }, 1000);
            
            setTimeout(() => {
                resultDiv.textContent += '3. 扫码成功，获取二维码内容 ✓\n';
            }, 1500);
            
            setTimeout(() => {
                resultDiv.textContent += '4. 验证二维码有效性 ✓\n';
            }, 2000);
            
            setTimeout(() => {
                resultDiv.textContent += '5. 提交签到记录 ✓\n';
            }, 2500);
            
            setTimeout(() => {
                resultDiv.textContent += '6. 签到完成！✨\n\n';
                resultDiv.textContent += `签到结果：\n`;
                resultDiv.textContent += `- 签到时间: ${new Date().toLocaleString()}\n`;
                resultDiv.textContent += `- 签到方式: ${isDingTalkEnv ? '钉钉扫码' : '模拟扫码'}\n`;
                resultDiv.textContent += `- 设备信息: ${navigator.userAgent.substring(0, 50)}...\n`;
                resultDiv.textContent += `- 状态: 签到成功 ✓`;
            }, 3000);
        }
    </script>
</body>
</html>
